


.resource-cube-icon {
    width: 12px;
    height: 12px;
    object-fit: contain;
    margin-right: 3px;
    filter: invert(1);
    vertical-align: -0.125em;
}

#environmentDisplay {
  color: white;
  font-size: 14px;
  text-shadow: 2px 2px 2px black;
  background: rgb(0 0 0 / 89%);
  padding: 10px;
  border-radius: 1px;
  width: 40%;
}

.subtitle-label {
  margin: 0 0 10px 0;
  padding: 3px;
  border-bottom: 1px solid rgb(255 255 255 / 65%);
  font-size: 12px;
  color: rgb(255 255 255 / 65%);
  display: flex;
  align-items: center;
  /* margin-bottom: 15px;
  padding: 3px;
  font-size: 12px;
  border-bottom: 1px solid rgb(255 255 255 / 65%);
  font-weight: bold;
  color: rgb(255 255 255 / 65%); */
  justify-content: space-between;
}

.panel-btn {
  background: rgb(18, 18, 18);
  color: var(--color-gray);
  border: var(--border-default-gray); /* 1px solid #1f1f1f;*/
  padding: 7px 12px;
  border-radius: 1px;
  display: flex;
  justify-content: center;
}


.fishing-btn-text,
.panel-btn-text {
  margin-left: 5px
}

.panel-btn:hover {
  animation: hover-color-inverse 0.4s ease forwards;
}

@keyframes hover-color-inverse {
    0% {
        filter: invert(0);
    }
    /* 50% {
        filter: invert(0.5);
    } */
    100% {
        filter: invert(1);
    }
}

.panel-btn:hover .cancel-icon {
  animation: rotate-90 0.5s ease forwards;
}

@keyframes rotate-90 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(90deg);
  }
}

/* Fishing system styles */
#actionBtnsContainer {
  display: flex;
  /* margin-bottom: 0px; */
  justify-content: flex-start;
  padding-bottom: 10px;
  /* border-bottom: 1px solid rgba(255, 255, 255, 0.2); */
  flex-wrap: wrap;
  flex-direction: row;
}

.modal {
  /* display: none; */
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}

.modal-content {
  background-color: rgba(30, 30, 30, 0.7);
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #88888824;
  border-radius: 5px;
  width: 300px;
  /* color: white; */
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #555;
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.close-modal {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-modal:hover {
  color: white;
}

.fishing-rod-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #555;
  border-radius: 5px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fishing-rod-item:hover {
  background-color: rgba(70, 70, 70, 0.5);
}

.rod-icon {
  font-size: 24px;
  margin-right: 15px;
}

.rod-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.fishing-mini-game {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(30, 30, 30, 0.95);
  border-radius: 10px;
  padding: 20px;
  width: 400px;
  z-index: 1000;
  color: white;
  text-align: center;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.fishing-game-header {
  margin-bottom: 20px;
}

.fishing-progress-container {
  margin: 30px 0;
}

.fishing-progress-bar {
  height: 30px;
  background-color: rgba(50, 50, 50, 0.8);
  border-radius: 15px;
  position: relative;
  overflow: hidden;
}

.fishing-catch-zone {
  position: absolute;
  height: 100%;
  background-color: rgba(46, 204, 113, 0.5);
  border: 2px solid rgba(46, 204, 113, 0.8);
  box-sizing: border-box;
}

.fishing-indicator {
  position: absolute;
  width: 10px;
  height: 100%;
  background-color: white;
  border-radius: 5px;
}

.fishing-instructions {
  margin: 20px 0;
  font-style: italic;
  color: #ccc;
}

.fishing-catch-button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.fishing-catch-button:hover {
  background-color: #2980b9;
}

/* Fishing result modal styles */
#fishingResultModal .modal-content {
  text-align: center;
}

.fishing-result-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px 0;
}

.fishing-result-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

#fishingResultOkButton {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 10px;
}

#fishingResultOkButton:hover {
  background-color: #2980b9;
}

.modal-footer {
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid #555;
  text-align: center;
}
