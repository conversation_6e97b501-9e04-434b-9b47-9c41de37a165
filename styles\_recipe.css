

/* Crafting system styles */
.recipe-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 8px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    cursor: pointer;
    transition: background 0.2s;
}

.recipe-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.recipe-item.selected {
    background: rgb(255 255 255 / 30%);
    /* box-shadow: 0 0 5px rgba(255, 255, 255, 0.3); */
}

.recipe-icon {
    font-size: 24px;
    margin-right: 10px;
}

.recipe-info {
    flex: 1;
}

.recipe-name {
    font-weight: bold;
}

.recipe-status {
    font-size: 12px;
    color: #aaa;
}

.recipe-item.available .recipe-status {
    color: #8f8;
}

.recipe-item.unavailable {
    opacity: 0.7;
}

.ingredient-list {
    padding-left: 20px;
}

.has-enough {
    color: #8f8;
}

.not-enough {
    color: #f88;
}

.recipe-result {
    display: flex;
    align-items: center;
    padding: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    margin-top: 10px;
}

.result-icon {
    font-size: 32px;
    margin-right: 15px;
}

.result-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.result-description {
    font-style: italic;
    color: #ccc;
    font-size: 12px;
    margin-bottom: 8px;
}

.recipe-detail-icon {
    font-size: 48px;
    text-align: center;
    margin: 10px 0;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}


.recipe-tabs {
    display: flex;
    margin-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
    /* height: 10%; */
}

.recipe-tab {
    padding: 5px 10px;
    margin-right: 5px;
    cursor: pointer;
    border-radius: 3px 3px 0 0;
    background: rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.recipe-tab:hover {
    background: rgba(0, 0, 0, 0.4);
}

.recipe-tab.active {
    background: rgba(0, 0, 0, 0.5);
    border-bottom: 2px solid #4a90e2;
}

#recipeListContent {
    height: 83%;
    overflow-y: auto;
}

#detailsPanel {
    background: rgb(0 0 0 / 93%);
    border-radius: 5px;
    padding: 10px;
    width: 55%;
}
