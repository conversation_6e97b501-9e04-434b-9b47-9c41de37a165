
import React, { useState, useCallback } from 'react';
import { InventoryItemStack } from 'src/Interfaces';
import { getText } from 'src/i18n';
import { useRootStore } from 'src/stores/rootStore';
import { ItemsGrid } from './Inventory/ItemsGrid';
import {
  DndContext,
  useSensor,
  useSensors,
  MouseSensor,
  TouchSensor,
  DragStartEvent,
  DragEndEvent,
  DragOverlay,
  rectIntersection,
  CollisionDetection,
} from "@dnd-kit/core";
import { snapCenterToCursor } from '@dnd-kit/modifiers';
import { InventoryItem } from './Inventory/InventoryItem';
import { useGroundStore } from 'src/stores/groundStore';
import { InventoryItemContent } from './Inventory/InventoryItemContent';
import { ItemDescriptionPopover } from './ItemDescriptionPopover';
import { ReactCrafting } from './ReactCrafting';
import { MacOSModal } from './WindowManagement/MacOSModal';
import { InventoryItemSlot } from './Inventory/InventoryItemSlot';
import { ItemLocation } from 'src/enums/common_enum';

// Fix collision detection for proper drag highlighting
const fixCursorSnapOffset: CollisionDetection = (args) => {
  // Bail out if keyboard activated
  if (!args.pointerCoordinates) {
    return rectIntersection(args);
  }
  const { x, y } = args.pointerCoordinates;
  const { width, height } = args.collisionRect;
  const updated = {
    ...args,
    // The collision rectangle is broken when using snapCenterToCursor. Reset
    // the collision rectangle based on pointer location and overlay size.
    collisionRect: {
      width,
      height,
      bottom: y + height / 2,
      left: x - width / 2,
      right: x + width / 2,
      top: y - height / 2,
    },
  };
  return rectIntersection(updated);
};

// export const GroundGrid = () => {
//   const [activeId, setActiveId] = useState<string | null>(null);
//   const [selectedInventoryStack, setSelectedInventoryStack] = useState<InventoryItemStack | null>(null);
//   const [selectedGroundStorageStack, setSelectedGroundStorageStack] = useState<InventoryItemStack | null>(null);
//   const [selectedInventoryItemRect, setSelectedInventoryItemRect] = React.useState<DOMRect | null>(null);

//   // Get inventory and storage data from stores
//   const itemStacks = useRootStore(state => state.itemStacks);
//   const setItemStacks = useRootStore(state => state.setItemStacks);
//   const currRegionIndex = useRootStore(state => state.currRegionIndex);
//   const groundStorageStacks = useGroundStore(state => state.allRegionGroundStacks[currRegionIndex]);
// //   const allRegionGroundStacks = useGroundStore(state => state.allRegionGroundStacks);
//   const removeGroundStackByIndex = useGroundStore(state => state.removeStackByIndex);
//   const swapGroundStorageStacks = useGroundStore(state => state.swapStacks);
//   const swapInventoryStacks = useRootStore(state => state.swapStacks);
//   const addStackByIndex = useGroundStore(state => state.addStackByIndex);
  

//   if (!itemStacks) {
//     return null;
//   }
//   if (!groundStorageStacks) {
//     return null;
//   }

//   console.log("groundStorageStacks", groundStorageStacks);
//   console.log("activeId", activeId);


//   // Drag and drop sensors
//   const sensors = useSensors(
//     useSensor(MouseSensor, {
//       activationConstraint: {
//         distance: 8,
//       },
//     }),
//     useSensor(TouchSensor, {
//       activationConstraint: {
//         delay: 250,
//         tolerance: 5,
//       },
//     })
//   );

//   const handleDragStart = useCallback((event: DragStartEvent) => {
//     setActiveId(event.active.id as string);
//   }, []);

//   const handleDragEnd = useCallback((event: DragEndEvent) => {
//     const { active, over } = event;
//     setActiveId(null);

//     if (!over) return;

//     const activeId = active.id as string;
//     const overId = over.id as string;

//     console.log('Drag end:', { activeId, overId });

//     // Parse IDs to determine source and destination
//     const isActiveFromInventory = activeId.startsWith('draggable-');
//     const isActiveFromStorage = activeId.startsWith('storage-draggable-');
//     const isOverInventory = overId.startsWith('droppable-');
//     const isOverStorage = overId.startsWith('storage-droppable-');

//     console.log('Drag classification:', {
//       isActiveFromInventory,
//       isActiveFromStorage,
//       isOverInventory,
//       isOverStorage
//     });

//     if (isActiveFromInventory && isOverStorage) {
//       // Moving from inventory to storage
//       const inventoryIndex = parseInt(activeId.replace('draggable-', ''));
//       const storageIndex = parseInt(overId.replace('storage-droppable-', ''));
      
//       const itemStackToMove = itemStacks[inventoryIndex];
//       console.log('Moving item to storage:', { inventoryIndex, storageIndex, itemStackToMove });

//       if (itemStackToMove) {
//         // Remove from inventory
//         const newItemStacks = [...itemStacks];
//         newItemStacks[inventoryIndex] = null;
//         setItemStacks(newItemStacks);

//         // Add to storage - maintain slot positions
//         addStackByIndex(currRegionIndex, storageIndex, itemStackToMove);

//       }
//     } else if (isActiveFromStorage && isOverInventory) {
//       // Moving from storage to inventory
//       const storageIndex = parseInt(activeId.replace('storage-draggable-', ''));
//       const inventoryIndex = parseInt(overId.replace('droppable-', ''));

//       const storageStack = groundStorageStacks[storageIndex];
//       if (storageStack) {
//         // Add to inventory
//         const newItemStacks = [...itemStacks];
//         newItemStacks[inventoryIndex] = storageStack;
//         console.log('222 Moving item to inventory:', { storageIndex, inventoryIndex, newItemStacks });
//         setItemStacks(newItemStacks);

//         // Remove from storage - find and remove the item at the specific slot
//         removeGroundStackByIndex(currRegionIndex, storageIndex);
//       }
//     } else if (isActiveFromStorage && isOverStorage) {
//       const fromIndex = parseInt(activeId.replace('storage-draggable-', ''));
//       const toIndex = parseInt(overId.replace('storage-droppable-', ''));

//       swapGroundStorageStacks(currRegionIndex, fromIndex, toIndex);
//     } else if (isActiveFromInventory && isOverInventory) {
//       const fromIndex = parseInt(activeId.replace('draggable-', ''));
//       const toIndex = parseInt(overId.replace('droppable-', ''));

//       swapInventoryStacks(fromIndex, toIndex);
//     }
//   }, [itemStacks, groundStorageStacks, setItemStacks, removeGroundStackByIndex, swapGroundStorageStacks, swapInventoryStacks]);

//   // Get the currently dragged item for the overlay
//   const activeItem = activeId ? (() => {
//     if (activeId.startsWith('draggable-')) {
//       const index = parseInt(activeId.replace('draggable-', ''));
//       return itemStacks[index];
//     } else if (activeId.startsWith('storage-draggable-')) {
//       const index = parseInt(activeId.replace('storage-draggable-', ''));
//       return groundStorageStacks[index];
//     }
//     return null;
//   })() : null;

//   const activeItemDef = activeItem ? Items[activeItem.itemId] : null;

  

//   const handleInventoryItemSelection = React.useCallback((itemStack: InventoryItemStack, element?: HTMLElement) => {
//       // Store the bounding rectangle of the selected item for popover positioning
//       if (element) {
//           // Get the bounding rectangle relative to the viewport
//           const rect = element.getBoundingClientRect();
//           console.log("Selected item rect:", rect);

//           // Set the rect first, then the selected stack to ensure the popover has the position
//           // before it tries to render
//           setSelectedInventoryItemRect(rect);

//           // Use a small timeout to ensure the rect is set before the popover renders
//           setTimeout(() => {
//               setSelectedInventoryStack(itemStack);
//           }, 0);
//       } else {
//           console.warn("No element reference provided for popover positioning");
//           setSelectedInventoryStack(itemStack);
//       }
//   }, [selectedInventoryStack]);

  
//   const handleGroundItemSelection = React.useCallback((itemStack: InventoryItemStack, element?: HTMLElement) => {
//       // Store the bounding rectangle of the selected item for popover positioning
//       if (element) {
//           // Get the bounding rectangle relative to the viewport
//           const rect = element.getBoundingClientRect();

//           // Set the rect first, then the selected stack to ensure the popover has the position
//           // before it tries to render
//           setSelectedGroundItemRect(rect);

//           // Use a small timeout to ensure the rect is set before the popover renders
//           setTimeout(() => {
//               setSelectedGroundStorageStack(itemStack);
//           }, 0);
//       } else {
//           console.warn("No element reference provided for popover positioning");
//           setSelectedGroundStorageStack(itemStack);
//       }
//   }, [selectedGroundStorageStack]);

//   return (
//     <>
    
//       <GroundBuildingsGrid/>

//       <DndContext
//         sensors={sensors}
//         onDragStart={handleDragStart}
//         onDragEnd={handleDragEnd}
//         collisionDetection={fixCursorSnapOffset}
//       >
//           {/* Storage Section */}
//           <div className="storage-section">
//             <div className="subtitle-label">
//                 <div>
//                   <img src = "images/ui_icons/ground.png" className="resource-cube-icon" />
//                   {`${getText('GROUND')} `}
//                 </div>
//             </div>
//             <div className="inventory-grid scrollable-container">
//               <ItemsGrid
//                 itemStacks={groundStorageStacks}
//                 selectedStack={selectedGroundStorageStack}
//                 setSelectedStack={handleGroundItemSelection}
//                 activeId={activeId}
//               />
//             </div>
//           </div>

//           {/* Player Inventory Section */}
//           <div className="storage-section">
//             <div className="subtitle-label">
//                 <div>
//                   <img src = "images/ui_icons/cube.svg" className="resource-cube-icon" />
//                   {`${getText('INVENTORY')} `}
//                 </div>
//             </div>
//             <ItemsGrid
//               itemStacks={itemStacks}
//               selectedStack={selectedInventoryStack}
//               setSelectedStack={handleInventoryItemSelection}
//               activeId={activeId}
//             />
//           </div>

//           {/* Drag Overlay for displaying image at mouse position */}
//           <DragOverlay modifiers={[snapCenterToCursor]} dropAnimation={null}>
//             {activeItem && activeItemDef ? (
//               <div className="drag-overlay-item">
//                 <InventoryItem itemStack={activeItem} itemDef={activeItemDef} />
//               </div>
//             ) : null}
//           </DragOverlay>
//       </DndContext>
      
//       {/* Render the popover when an item is selected */}
//       {selectedInventoryStack && (
//           <ItemDescriptionPopover
//               selectedStack={selectedInventoryStack}
//               // itemDef={itemDef}
//               anchorRect={selectedInventoryItemRect}
//               isOpen={!!selectedInventoryStack}
//               onClose={() => {
//                   setSelectedInventoryStack(null);
//                   setSelectedInventoryItemRect(null);
//               }}
//           />
//       )}
//       {selectedGroundStorageStack && (
//           <ItemDescriptionPopover
//               selectedStack={selectedGroundStorageStack}
//               // itemDef={itemDef}
//               anchorRect={selectedGroundItemRect}
//               isOpen={!!selectedGroundStorageStack}
//               onClose={() => {
//                   setSelectedGroundStorageStack(null);
//                   setSelectedGroundItemRect(null);
//               }}
//           />
//       )}
//     </>
//   );
// };


export const GroundStorageGrid = () => {

  const [ selectedGroundStorageStack, setSelectedGroundStorageStack ] = React.useState<InventoryItemStack | null>(null);
  const [ selectedGroundItemRect, setSelectedGroundItemRect] = React.useState<DOMRect | null>(null);
  const [ activeId, setActiveId ] = useState<string | null>(null);

  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundStorageStacks = useGroundStore(state => state.allRegionGroundStacks[currRegionIndex]);
  const swapGroundStorageStacks = useGroundStore(state => state.swapStacks);

  return (
    <div className="building-grid-panel">
      <div className="subtitle-label">
          <div>
            <img src = "images/ui_icons/home.svg" className="resource-cube-icon" />
            {`${getText('GROUND ITEMS')} `}
          </div>
      </div>
      <CommonGrid
        selectedStack={selectedGroundStorageStack}
        setSelectedStack={setSelectedGroundStorageStack}
        selectedRect={selectedGroundItemRect}
        setSelectedRect={setSelectedGroundItemRect}
        activeId={activeId}
        setActiveId={setActiveId}
        itemStacks={groundStorageStacks}
        swapStacks={swapGroundStorageStacks}
        currRegionIndex={currRegionIndex}
        location={ItemLocation.Ground}
        // gridMaxheight = '38%'
      />
    </div>
  );
}

export const GroundBuildingsGrid = () => {

  const [ selectedGroundBuilding, setSelectedGroundBuilding ] = React.useState<InventoryItemStack | null>(null);
  const [ selectedBuildingRect, setSelectedBuildingRect] = React.useState<DOMRect | null>(null);
  const [ activeId, setActiveId ] = useState<string | null>(null);

  const currRegionIndex = useRootStore(state => state.currRegionIndex);
  const groundBuildings = useGroundStore(state => state.allRegionGroundBuildings[currRegionIndex]);
  const swapGroundBuildings = useGroundStore(state => state.swapGroundBuildings);

  return (
    <div className="building-grid-panel">
      <div className="subtitle-label">
          <div>
            <img src = "images/ui_icons/home.svg" className="resource-cube-icon" />
            {`${getText('GROUND')} `}
          </div>
      </div>
      <CommonGrid
        selectedStack={selectedGroundBuilding}
        setSelectedStack={setSelectedGroundBuilding}
        selectedRect={selectedBuildingRect}
        setSelectedRect={setSelectedBuildingRect}
        activeId={activeId}
        setActiveId={setActiveId}
        itemStacks={groundBuildings}
        swapStacks={swapGroundBuildings}
        currRegionIndex={currRegionIndex}
        location={ItemLocation.GroundBuilding}
        // gridMaxheight = '88%'
      />
    </div>
  );
}


const CommonGrid = ({
  selectedStack,
  setSelectedStack,
  selectedRect,
  setSelectedRect,
  activeId,
  setActiveId,
  itemStacks,
  swapStacks,
  currRegionIndex,
  location
  // gridMaxheight
}) => {

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250,
        tolerance: 5,
      },
    })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    console.log('Drag end:', { activeId, overId });

    swapStacks(currRegionIndex, parseInt(activeId.replace('draggable-', '')), parseInt(overId.replace('droppable-', '')));
  }, [swapStacks, currRegionIndex]);

  
  const handleItemStackSelection = React.useCallback((itemStack: InventoryItemStack, element?: HTMLElement) => {
      // Store the bounding rectangle of the selected item for popover positioning
      if (element) {
          // Get the bounding rectangle relative to the viewport
          const rect = element.getBoundingClientRect();

          // Set the rect first, then the selected stack to ensure the popover has the position
          // before it tries to render
          setSelectedRect(rect);

          // Use a small timeout to ensure the rect is set before the popover renders
          setTimeout(() => {
              setSelectedStack(itemStack);
          }, 0);
      } else {
          console.warn("No element reference provided for popover positioning");
          setSelectedStack(itemStack);
      }
  }, [selectedStack]);

  
  // Get the currently dragged item for the overlay
  const activeItem = activeId ? (() => {
    if (activeId.startsWith('draggable-')) {
      const index = parseInt(activeId.replace('draggable-', ''));
      return itemStacks[index];
    } 
    console.log("Not a ground building", activeId);
    return null;
  })() : null;

  return (
    <>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        collisionDetection={fixCursorSnapOffset}
      >
          <div
            className="buildings-grid scrollable-container"
            // style={{maxHeight: gridMaxheight ? gridMaxheight : 'none'}}
          >
            <ItemsGrid
              itemStacks={itemStacks}
              selectedStack={selectedStack}
              setSelectedStack={handleItemStackSelection}
              activeId={activeId}
            />
          </div>
          
          {/* Drag Overlay for displaying image at mouse position */}
          <DragOverlay modifiers={[snapCenterToCursor]} dropAnimation={null}>
            {activeItem ? (
              <div className="drag-overlay-item">
                <InventoryItem itemStack={activeItem} itemDef={activeItem.itemDef} />
              </div>
            ) : null}
          </DragOverlay>
      </DndContext>
      
      {selectedStack && (
          <ItemDescriptionPopover
              selectedStack={selectedStack}
              // itemDef={itemDef}
              anchorRect={selectedRect}
              isOpen={!!selectedStack}
              onClose={() => {
                  setSelectedStack(null);
                  setSelectedRect(null);
              }}
              location={location}
          />)}
      </>
    );
};





// const InventoryItemSlot = React.memo((props: {
//     itemStack: InventoryItemStack,
//     setSelectedStack: (itemStack: InventoryItemStack, element?: HTMLElement) => void,
//     stackIndex: number,
//     isActive: boolean,
//     isSelected: boolean,
// }) => {
//     console.log("InventoryItemSlot rendered!!!!", props.stackIndex, props.isActive);

//     // Create the data object
//     const draggableData = {
//         index: props.stackIndex,
//         itemStack: props.itemStack
//     };

//     // Use the DragDropWrapper to isolate the DnD context changes
//     // Pass isSelected directly to DragDropWrapper so it will re-render when selection changes
//     return (
//         <StorageDragDropWrapper
//             id={`${props.stackIndex}`}
//             data={draggableData}
//             disabled={!props.itemStack}
//             isSelected={props.isSelected}
//             // setSelectedStack={props.setSelectedStack}
//         >
//             {({ ref, isDragging, isOver, attributes, listeners, isSelected }) => (
//                 <InventoryItemContent
//                     itemStack={props.itemStack}
//                     isSelected={isSelected}
//                     setSelectedStack={props.setSelectedStack}
//                     isDragging={isDragging}
//                     isOver={isOver}
//                     attributes={attributes}
//                     listeners={listeners}
//                     innerRef={ref}
//                 />
//             )}
//         </StorageDragDropWrapper>
//     );
// }, (prevProps, nextProps) => {
//     // Custom equality function to prevent unnecessary re-renders
//     if (prevProps.isActive !== nextProps.isActive) return false;
//     if (prevProps.isSelected !== nextProps.isSelected) {
//         console.log("selected changed 000", nextProps.stackIndex, prevProps.isSelected, nextProps.isSelected);
//         return false;
//     }

//     // Check if itemStack has changed
//     if (!prevProps.itemStack && !nextProps.itemStack) return true;
//     if (!prevProps.itemStack || !nextProps.itemStack) return false;

//     return (
//         prevProps.itemStack.uuid === nextProps.itemStack.uuid &&
//         prevProps.itemStack.quantity === nextProps.itemStack.quantity
//     );
// });


