import React = require("react");
import { getText } from "src/i18n";
import { showPromptOverlay } from "src/util";
import { ResourceTypes } from "src/enums/common_enum";
import { CraftableItem } from "src/Interfaces";
import { ItemIcon } from "./common";
import { useRootStore } from "../stores/rootStore";
import { RecipeDetailsModal } from "./RecipeDetailsModal";
import { CRAFTABLES_MAP } from "src/enums/resources";

// Define the recipe categories
const RecipeCategories = [
    ResourceTypes.TOOL,
    ResourceTypes.MATERIAL,
    ResourceTypes.Equipable,
    ResourceTypes.TRANSPORT,
    ResourceTypes.BUILDING
];

// Component for the recipe category tabs
const RecipeCategoryTabs: React.FC<{
    selectedCategoryId: string;
    onSelectCategory: (categoryId: string) => void;
}> = ({ selectedCategoryId, onSelectCategory }) => {
    return (
        <div id="recipeCategoryTabs" className="recipe-tabs">
            {RecipeCategories.map((category) => (
                <div
                    key={category.id}
                    className={`recipe-tab ${selectedCategoryId === category.id ? 'active' : ''}`}
                    onClick={() => onSelectCategory(category.id)}
                >
                    <div>
                        <ItemIcon itemDef={category} invertColor={true} maxWidth={18} />
                    </div> 
                    <div>{category.name}</div>
                </div>
            ))}
        </div>
    );
};

// Component for a single recipe item in the list
const RecipeItem: React.FC<{
    recipe: CraftableItem;
    canCraft: boolean;
    isSelected: boolean;
    onSelect: (recipe: CraftableItem) => void;
}> = ({ recipe, canCraft, isSelected, onSelect }) => {
    return (
        <div
            className={`recipe-item ${canCraft ? 'available' : 'unavailable'} ${isSelected ? 'selected' : ''}`}
            onClick={() => onSelect(recipe)}
        >
            <div className="recipe-icon">
                <ItemIcon itemDef={recipe} />
            </div>
            <div className="recipe-info">
                <div className="recipe-name">{recipe.name}</div>
                <div className="recipe-status">
                    {canCraft ? '✅ Available' : '❌ Missing ingredients'}
                </div>
            </div>
        </div>
    );
};

// Component for the recipe list
const RecipeList: React.FC<{
    recipes: { [key: string]: CraftableItem };
    checkIngredients: (recipe: CraftableItem) => boolean;
    selectedRecipe: CraftableItem | null;
    onSelectRecipe: (recipe: CraftableItem) => void;
}> = ({ recipes, checkIngredients, selectedRecipe, onSelectRecipe }) => {
    return (
        <div id="recipeListContent" className="scrollable-container">
            {Object.values(recipes).length > 0 ? (
                Object.values(recipes).map((recipe) => (
                    <RecipeItem
                        key={recipe.id}
                        recipe={recipe}
                        canCraft={checkIngredients(recipe)}
                        isSelected={selectedRecipe?.id === recipe.id}
                        onSelect={onSelectRecipe}
                    />
                ))
            ) : (
                <p>{getText('No recipes available in this category')}</p>
            )}
        </div>
    );
};

// // Component for the recipe details panel
// const RecipeDetails: React.FC<{
//     recipe: CraftableItem;
//     canCraft: boolean;
//     getIngredientQuantity: (ingredientId: string) => number;
//     onCraft: () => void;
// }> = ({ recipe, canCraft, getIngredientQuantity, onCraft }) => {
//     if (!recipe) return null;

//     return (
//         <div>
//             <h3>{recipe.name}</h3>
//             <div className="recipe-detail-icon">
//                 <ItemIcon itemDef={recipe} />
//             </div>
//             <p>{recipe.description}</p>
//             <h4>{getText('Ingredients')}:</h4>
//             <ul className="ingredient-list">
//                 {recipe.ingredients?.map((ingredient) => {
//                     const available = getIngredientQuantity(ingredient.itemDef.id);
//                     const hasEnough = available >= ingredient.quantity;
//                     return (
//                         <li key={ingredient.itemDef.id} className={hasEnough ? 'has-enough' : 'not-enough'}>
//                             {Items[ingredient.itemDef.id]?.name} ({available}/{ingredient.quantity})
//                         </li>
//                     );
//                 })}
//             </ul>
            
//             {/* <div className="building-resources">
//                 {recipe.ingredients?.map((ingredient, index) => (
//                     <span key={index}>{quantityMap[res.itemDef.id] >= res.quantity ? '✅' : '❌'} {res.quantity} {res.itemDef.name}</span>
//                 )).reduce((prev, curr) => prev === null ? curr : <>{prev}, {curr}</>, null)}
//             </div> */}
            
//             <h4>{getText('Result')}:</h4>
//             <div className="recipe-result">
//                 <div className="result-icon">
//                     <ItemIcon itemDef={recipe} />
//                 </div>
//                 <div className="result-info">
//                     <div className="result-name">{recipe.name}</div>
//                     <div className="result-description">{recipe.description}</div>
//                     {recipe.type.name === "Edible" && (
//                         <div className="result-stats">
//                             {recipe.food > 0 && <div>🍖 {getText('Food')}: +{recipe.food}</div>}
//                             {recipe.water > 0 && <div>💧 {getText('Water')}: +{recipe.water}</div>}
//                             {recipe.energy > 0 && <div>⚡ {getText('Energy')}: +{recipe.energy}</div>}
//                             {recipe.health > 0 && <div>❤️ {getText('Health')}: +{recipe.health}</div>}
//                         </div>
//                     )}
//                     {recipe.type.name === "Medicinal" && (
//                         <div className="result-stats">
//                             <div>❤️ {getText('Health')}: +{recipe.health}</div>
//                         </div>
//                     )}
//                 </div>
//             </div>
//             <button
//                 id="craftButton"
//                 onClick={onCraft}
//                 disabled={!canCraft}
//                 style={{
//                     background: canCraft ? 'rgba(0,100,0,0.7)' : 'rgba(100,0,0,0.7)',
//                     cursor: canCraft ? 'pointer' : 'not-allowed',
//                     opacity: canCraft ? '1' : '0.7',
//                 }}
//             >
//                 {getText('Craft Item')}
//             </button>
//         </div>
//     );
// };

// Main crafting component
export const ReactCrafting: React.FC = React.memo(() => {
    console.log('ReactCrafting component rendered');

    // Use Zustand store instead of context
    const itemStacks = useRootStore(state => state.itemStacks);

    // State for the crafting system
    const [selectedCategoryId, setSelectedCategoryId] = React.useState<string>('Tool');
    const [selectedRecipe, setSelectedRecipe] = React.useState<CraftableItem | null>(null);
    const [showRecipeModal, setShowRecipeModal] = React.useState(false);

    // Get the recipes for the selected category
    const recipes = React.useMemo(() => {
        return CRAFTABLES_MAP[selectedCategoryId] || {};
    }, [selectedCategoryId]);

    // Function to check if the player has the required ingredients for a recipe
    const checkIngredients = React.useCallback((recipe: CraftableItem): boolean => {
        if (!recipe.ingredients) return true;

        for (const ingredient of recipe.ingredients) {
            const totalQuantity = getIngredientQuantity(ingredient.itemDef.id);
            if (totalQuantity < ingredient.quantity) {
                return false;
            }
        }
        return true;
    }, [itemStacks]);

    // Function to get the quantity of an ingredient in the inventory
    const getIngredientQuantity = React.useCallback((ingredientId: string): number => {
        return itemStacks.reduce((total, stack) => {
            if (stack && stack.itemId === ingredientId) {
                return total + stack.quantity;
            }
            return total;
        }, 0);
    }, [itemStacks]);

    // Function to handle category selection
    const handleCategorySelect = (selectedCategoryId: string) => {
        setSelectedCategoryId(selectedCategoryId);
        setSelectedRecipe(null);
    };

    // Function to handle recipe selection
    const handleRecipeSelect = (recipe: CraftableItem) => {
        setSelectedRecipe(recipe);
        setShowRecipeModal(true);
    };

    return (
        <>
            <div id="recipeList">
                {/* <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
                    {getText('Available Recipes')}
                </div> */}
                <RecipeCategoryTabs
                    selectedCategoryId={selectedCategoryId}
                    onSelectCategory={handleCategorySelect}
                />
                <RecipeList
                    recipes={recipes}
                    checkIngredients={checkIngredients}
                    selectedRecipe={selectedRecipe}
                    onSelectRecipe={handleRecipeSelect}
                />
            </div>
            {/* <div id="detailsPanel">
                {selectedRecipe ? (
                    <RecipeDetails
                        recipe={selectedRecipe}
                        canCraft={checkIngredients(selectedRecipe)}
                        getIngredientQuantity={getIngredientQuantity}
                        onCraft={handleCraft}
                    />
                ) : (
                    <>
                        <h3>{getText('Recipe Details')}</h3>
                        <p>{getText('Select a recipe to view details')}</p>
                    </>
                )}
            </div> */}

            {/* Recipe Details Modal */}
            {selectedRecipe && (
                <RecipeDetailsModal
                    recipe={selectedRecipe}
                    isOpen={showRecipeModal}
                    onClose={() => {
                        setShowRecipeModal(false);
                        setSelectedRecipe(null);
                    }}
                    canCraft={checkIngredients(selectedRecipe)}
                    getIngredientQuantity={getIngredientQuantity}
                    selectedRecipe={selectedRecipe}
                    onCraft={() => {
                        // Show success message
                        showPromptOverlay(getText('Crafted') + `: ${selectedRecipe.name}`);
                        setShowRecipeModal(false);
                        setSelectedRecipe(null);
                    }}
                />
            )}
        </>
    );
});