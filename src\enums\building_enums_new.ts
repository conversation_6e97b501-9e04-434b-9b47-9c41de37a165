import { gameStatus, startProgressBar } from "src/gameinfo";
import { getText } from "src/i18n";
import { CraftableItem, PlacedBuilding, StorageBuilding } from "src/Interfaces";
// import { openFireLightUpInterface, openCookingInterface } from "src/cooking";
// import React from 'react';
// import { createRoot } from 'react-dom/client';
// import { StorageModal } from "src/components/StorageModal";
import { MATERIALS } from "./Materials";
import { WATER } from "./food_enums";
import { CRAFTABLE_MATERIALS } from "./CRAFTABLE_enums";
import { RARITY, ResourceTypes } from "./common_enum";


console.log("Loading building_enums_new.js");

// const INTERACTIONS = {
//     sleep: {
//         id: 'sleep',
//         get name() { return getText('interaction_sleep'); },
//         icon: '💤',
//         action: function() {
//             startProgressBar(getText('Sleeping'), 480, () => { // 8 hours in game
//                 gameStatus.energy = 100;
//             });
//         },
//         duration: 480 // 8 hours in game
//     }
// }

const STORAGE_BUILDINGS: { [key: string]: CraftableItem} = {
    SMALL_STORAGE_BOX: {
        id: 'SMALL_STORAGE_BOX',
        get name() { return getText('SMALL_STORAGE_BOX'); },
        get description() { return getText('SMALL_STORAGE_BOX_desc'); },
        type: ResourceTypes.BUILDING,
        icon: '📦',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: CRAFTABLE_MATERIALS.BoneGlue, quantity: 1 },
        ],
        interactions: [
            {
                id: 'store',
                get name() { return getText('interaction_store'); },
                icon: '🗃️',
                action: function(building) {
                    // Open storage interface
                    openStorageInterface(building);
                }
            }
        ],
        storageCapacity: 20
    },
    MEDIUM_STORAGE_BOX: {
        id: 'MEDIUM_STORAGE_BOX',
        get name() { return getText('MEDIUM_STORAGE_BOX'); },
        get description() { return getText('MEDIUM_STORAGE_BOX_desc'); },
        type: ResourceTypes.BUILDING,
        icon: '📦',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: CRAFTABLE_MATERIALS.BoneGlue, quantity: 1 },
        ],
        interactions: [
            {
                id: 'store',
                get name() { return getText('interaction_store'); },
                icon: '🗃️',
                action: function(building) {
                    // Open storage interface
                    openStorageInterface(building);
                }
            }
        ],
        storageCapacity: 40
    },
}

export const CRAFTABLE_BUILDINGS = {
    ...STORAGE_BUILDINGS,
    solar_distiller: {
        id: 'solar_distiller',
        get name() { return getText('solar_distiller'); },
        get description() { return getText('solar_distiller'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/solar_distiller.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: CRAFTABLE_MATERIALS.BoneGlue, quantity: 1 },
        ]
    },
    water_tank: {
        id: 'water_tank',
        get name() { return getText('water_tank'); },
        get description() { return getText('water_tank'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/water_tank.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: CRAFTABLE_MATERIALS.BoneGlue, quantity: 1 },
        ]
    },
    windmill: {
        id: 'windmill',
        get name() { return getText('windmill'); },
        get description() { return getText('windmill'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/windmill.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: CRAFTABLE_MATERIALS.BoneGlue, quantity: 1 },
        ]
    },
    wind_turbine: {
        id: 'wind_turbine',
        get name() { return getText('wind_turbine'); },
        get description() { return getText('wind_turbine'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/wind_turbine.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
            { itemDef: CRAFTABLE_MATERIALS.BoneGlue, quantity: 1 },
        ]
    },
    chest: {
        id: 'chest',
        get name() { return getText('chest'); },
        get description() { return getText('chest'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/chest.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ]
    },
    animal_farm: {
        id: 'animal_farm',
        get name() { return getText('animal_farm'); },
        get description() { return getText('animal_farm'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/animal_farm.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ]
    },
    hive: {
        id: 'hive',
        get name() { return getText('hive'); },
        get description() { return getText('hive'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/hive.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ]
    },
    farm: {
        id: 'farm',
        get name() { return getText('farm'); },
        get description() { return getText('farm'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/farm.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 1 },
            { itemDef: MATERIALS.Stone, quantity: 1 },
        ]
    },
    SMALL_HOUSE: {
        id: 'SMALL_HOUSE',
        get name() { return getText('building_small_house'); },
        get description() { return getText('building_small_house_desc'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/wooden_house.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 10 },
            { itemDef: MATERIALS.Stone, quantity: 5 },
        ],
        // interactions: [INTERACTIONS.sleep],
    },
    MEDIUM_HOUSE: {
        id: 'MEDIUM_HOUSE',
        get name() { return getText('building_small_house'); },
        get description() { return getText('building_small_house_desc'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/wooden_house2.png',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 10 },
            { itemDef: MATERIALS.Stone, quantity: 5 },
        ],
        // interactions: [INTERACTIONS.sleep],
    },
    LARGE_HOUSE: {
        id: 'LARGE_HOUSE',
        get name() { return getText('building_small_house'); },
        get description() { return getText('building_small_house_desc'); },
        type: ResourceTypes.BUILDING,
        icon: '🏠',
        ingredients: [
            { itemDef: MATERIALS.Log, quantity: 10 },
            { itemDef: MATERIALS.Stone, quantity: 5 },
        ],
        // interactions: [INTERACTIONS.sleep],
    },
    // FIRE_PIT: {
    //     id: 'FIRE_PIT',
    //     get name() { return getText('building_fire_pit'); },
    //     get description() { return getText('building_fire_pit_desc'); },
    //     icon: '🔥',
    //     ingredients: [
    //         { itemDef: MATERIALS.Log, quantity: 10 },
    //         { itemDef: MATERIALS.Stone, quantity: 5 },
    //     ],
    //     interactions: [
    //         {
    //             id: 'light_fire',
    //             get name() { return getText('interaction_light_fire'); },
    //             icon: '🔥',
    //             action: function(building) {
    //                 // Check if fire is already lit
    //                 if (building.isLit) {
    //                     return;
    //                 }
    //                 // Open fire light up interface
    //                 openFireLightUpInterface(building);
    //             },
    //             condition: function(building) {
    //                 return !building.isLit;
    //             }
    //         },
    //         {
    //             id: 'cook',
    //             get name() { return getText('interaction_cook'); },
    //             icon: '🍳',
    //             action: function(building) {
    //                 // Open cooking interface
    //                 openCookingInterface(building);
    //             },
    //             condition: function(building) {
    //                 return building.isLit;
    //             }
    //         }
    //     ],
    //     width: 1,
    //     height: 1
    // },
    WorkStation: {
        id: 'WorkStation',
        get name() { return getText('WorkStation'); },
        get description() { return getText('WorkStation'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/buildings/workstation.png',
        ingredients: [
            { itemDef: MATERIALS.Grass, quantity: 0 }
        ],
        interactions: [
            {
                id: 'clean_stone',
                get name() { return getText('clean_stone'); },
                icon: '🍳',
                action: function() {
                }
            }],
        multiIcons: true,
        hideInCraftList: true,
    },
    GARDEN_PLOT: {
        id: 'GARDEN_PLOT',
        get name() { return getText('building_garden_plot'); },
        get description() { return getText('building_garden_plot_desc'); },
        type: ResourceTypes.BUILDING,
        icon: '🌱',
        ingredients: [
            { itemDef: MATERIALS.Grass, quantity: 10 },
            { itemDef: WATER, quantity: 5 },
            { itemDef: MATERIALS.Log, quantity: 2 },
        ],
        interactions: [
            {
                id: 'plant',
                get name() { return getText('interaction_plant'); },
                icon: '🌿',
                action: function() {
                    // Open planting interface
                    openPlantingInterface();
                }
            },
            {
                id: 'harvest',
                get name() { return getText('interaction_harvest'); },
                icon: '🌾',
                action: function() {
                    startProgressBar(getText('Harvesting'), 30, () => {
                        // Add harvested items to inventory
                        // window.add.addItem('Vegetable', 3);
                    });
                },
                duration: 30 // 30 minutes in game
            }
        ],
    },
    TRADING_POST: {
        id: 'TRADING_POST',
        get name() { return getText('building_trading_post'); },
        get description() { return getText('building_trading_post_desc'); },
        type: ResourceTypes.BUILDING,
        icon: '🌲',
        ingredients: [
            { itemDef: MATERIALS.Grass, quantity: 1 },
            { itemDef: WATER, quantity: 1 },
        ],
        interactions: [],
    }
}


export const NATURAL_BUILDINGS: { [key: string]: CraftableItem} = {
    GRASS_PLANT: {
        id: 'GRASS_PLANT',
        get name() { return getText('building_grass_plant'); },
        get description() { return getText('building_grass_plant_desc'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/snowtree1.png',
        rarity: RARITY.UNCOMMON,
    },
    pinetree: {
        id: 'pinetree',
        get name() { return getText('pinetree'); },
        get description() { return getText('pinetree'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/trees/pinetree.png',
        rarity: RARITY.UNCOMMON,
        ingredients: [
            { itemDef: MATERIALS.Grass, quantity: 0 }
        ],
        interactions: [],
    },
    TREE: {
        id: 'TREE',
        get name() { return getText('TREE'); },
        get description() { return getText('TREE'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/tree_buildings/tree.png',
        rarity: RARITY.UNCOMMON,
        ingredients: [
            { itemDef: MATERIALS.Grass, quantity: 0 }
        ]
    },
    ConiferousTree: {
        id: 'ConiferousTree',
        get name() { return getText('ConiferousTree'); },
        get description() { return getText('ConiferousTree'); },
        type: ResourceTypes.BUILDING,
        icon: 'images/tree_buildings/conifer.png',
        rarity: RARITY.UNCOMMON,
        ingredients: [
            { itemDef: MATERIALS.Grass, quantity: 0 }
        ]
    },
    CoconutTree: {
        id: 'CoconutTree',
        get name() { return getText('CoconutTree'); },
        get description() { return getText('desc_CoconutTree'); },
        rarity: RARITY.UNCOMMON,
        type: ResourceTypes.BUILDING,
        icon: 'images/tree_buildings/palm_tree.png',
    },
    BigStone: {
        id: 'BigStone',
        get name() { return getText('BigStone'); },
        get description() { return getText('BigStone'); },
        icon: 'images/rocks/rock1.png,images/rocks/rock2.png,images/rocks/rock3.png,images/rocks/rock4.png',
        type: ResourceTypes.BUILDING,
        rarity: RARITY.UNCOMMON,
    },
};




export const BUILDINGS: { [key: string]: CraftableItem} = {
    ...CRAFTABLE_BUILDINGS,
    ...NATURAL_BUILDINGS,
};


// // Storage system to manage modal instances
// const storageSystem = {
//     currentBuilding: null as PlacedBuilding | null,
//     storageModalInstance: null as any
// };

// // Helper function to ensure modal container exists
// function ensureModalContainer(containerId: string): HTMLElement {
//     let container = document.getElementById(containerId);
//     if (!container) {
//         container = document.createElement('div');
//         container.id = containerId;
//         document.body.appendChild(container);
//     }
//     return container;
// }

// // Functions to be implemented for interactions
// function openStorageInterface(building: PlacedBuilding) {
//     console.log('Opening storage interface for', building);

//     // Close any existing storage modal
//     if (storageSystem.storageModalInstance) {
//         storageSystem.storageModalInstance.unmount();
//         storageSystem.storageModalInstance = null;
//     }

//     storageSystem.currentBuilding = building;

//     // Create a unique container for this modal
//     const containerId = `storage-modal-${building.id}`;
//     const container = ensureModalContainer(containerId);

//     // Create a new root and render the modal
//     const modalRoot = createRoot(container);
//     storageSystem.storageModalInstance = modalRoot;

//     modalRoot.render(
//         React.createElement(StorageModal, {
//             building,
//             isOpen: true,
//             portalId: `storage-portal-${building.id}`,
//             onClose: () => {
//                 console.log('Closing storage modal');
//                 // Update the building reference before closing the modal
//                 storageSystem.currentBuilding = null;

//                 // Unmount the modal completely after a short delay
//                 setTimeout(() => {
//                     if (storageSystem.storageModalInstance) {
//                         storageSystem.storageModalInstance.unmount();
//                         storageSystem.storageModalInstance = null;

//                         // Remove the container
//                         if (container.parentNode) {
//                             container.parentNode.removeChild(container);
//                         }
//                     }
//                 }, 100);
//             }
//         })
//     );
// }

function openPlantingInterface() {
    console.log('Opening planting interface');
    // Implementation would go here
}