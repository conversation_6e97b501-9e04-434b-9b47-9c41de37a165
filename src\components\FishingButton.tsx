import React = require("react");
import { FishingModal } from "./ReactFishing";
import { getText } from "src/i18n";

export const FishingButton = () => {
    const [showFishingDialog, setShowFishingDialog] = React.useState(false);

    const handleOpenFishing = () => {
        setShowFishingDialog(true);
    };

    const handleCloseFishing = () => {
        setShowFishingDialog(false);
    };

    return (
        <> 
            <button
                className="panel-btn"
                onClick={handleOpenFishing}
            >
            <img src="images/fish/fishing.png" width={14} height={14} />
            <div className="panel-btn-text">{getText("Fishing")}</div>
                
            </button>

            {showFishingDialog && (
                <FishingModal onClose={handleCloseFishing} />
            )}
        </>
    );
}